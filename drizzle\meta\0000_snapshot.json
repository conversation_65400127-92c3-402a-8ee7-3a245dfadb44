{"version": "6", "dialect": "sqlite", "id": "b34e80b1-cee8-41fe-9bd1-208a3961701c", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"file_uploads": {"name": "file_uploads", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "property_id": {"name": "property_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "tenant_id": {"name": "tenant_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "file_name": {"name": "file_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "file_type": {"name": "file_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "file_url": {"name": "file_url", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "upload_type": {"name": "upload_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"file_uploads_user_id_users_id_fk": {"name": "file_uploads_user_id_users_id_fk", "tableFrom": "file_uploads", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "file_uploads_property_id_properties_id_fk": {"name": "file_uploads_property_id_properties_id_fk", "tableFrom": "file_uploads", "tableTo": "properties", "columnsFrom": ["property_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "file_uploads_tenant_id_tenants_id_fk": {"name": "file_uploads_tenant_id_tenants_id_fk", "tableFrom": "file_uploads", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "leases": {"name": "leases", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "property_id": {"name": "property_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "tenant_id": {"name": "tenant_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "start_date": {"name": "start_date", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "end_date": {"name": "end_date", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "monthly_rent": {"name": "monthly_rent", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'ZAR'"}, "security_deposit": {"name": "security_deposit", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false}, "lease_terms": {"name": "lease_terms", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_signed": {"name": "is_signed", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "signed_at": {"name": "signed_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "signature_data": {"name": "signature_data", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "document_url": {"name": "document_url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"leases_property_id_properties_id_fk": {"name": "leases_property_id_properties_id_fk", "tableFrom": "leases", "tableTo": "properties", "columnsFrom": ["property_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "leases_tenant_id_tenants_id_fk": {"name": "leases_tenant_id_tenants_id_fk", "tableFrom": "leases", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "maintenance_requests": {"name": "maintenance_requests", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "tenant_id": {"name": "tenant_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "property_id": {"name": "property_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "severity": {"name": "severity", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'minor'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'pending'"}, "photo_file_ids": {"name": "photo_file_ids", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "assigned_to": {"name": "assigned_to", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "completed_at": {"name": "completed_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"maintenance_requests_tenant_id_tenants_id_fk": {"name": "maintenance_requests_tenant_id_tenants_id_fk", "tableFrom": "maintenance_requests", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "maintenance_requests_property_id_properties_id_fk": {"name": "maintenance_requests_property_id_properties_id_fk", "tableFrom": "maintenance_requests", "tableTo": "properties", "columnsFrom": ["property_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "maintenance_requests_assigned_to_users_id_fk": {"name": "maintenance_requests_assigned_to_users_id_fk", "tableFrom": "maintenance_requests", "tableTo": "users", "columnsFrom": ["assigned_to"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "notifications": {"name": "notifications", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "related_id": {"name": "related_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_read": {"name": "is_read", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "read_at": {"name": "read_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"notifications_user_id_users_id_fk": {"name": "notifications_user_id_users_id_fk", "tableFrom": "notifications", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "payment_records": {"name": "payment_records", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "tenant_id": {"name": "tenant_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "lease_id": {"name": "lease_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "amount": {"name": "amount", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'ZAR'"}, "payment_date": {"name": "payment_date", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "payment_method": {"name": "payment_method", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "receipt_file_id": {"name": "receipt_file_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_verified": {"name": "is_verified", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "verified_at": {"name": "verified_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "verified_by": {"name": "verified_by", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"payment_records_tenant_id_tenants_id_fk": {"name": "payment_records_tenant_id_tenants_id_fk", "tableFrom": "payment_records", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payment_records_lease_id_leases_id_fk": {"name": "payment_records_lease_id_leases_id_fk", "tableFrom": "payment_records", "tableTo": "leases", "columnsFrom": ["lease_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payment_records_receipt_file_id_file_uploads_id_fk": {"name": "payment_records_receipt_file_id_file_uploads_id_fk", "tableFrom": "payment_records", "tableTo": "file_uploads", "columnsFrom": ["receipt_file_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "payment_records_verified_by_users_id_fk": {"name": "payment_records_verified_by_users_id_fk", "tableFrom": "payment_records", "tableTo": "users", "columnsFrom": ["verified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "properties": {"name": "properties", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "owner_id": {"name": "owner_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "state": {"name": "state", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "zip_code": {"name": "zip_code", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "country": {"name": "country", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'South Africa'"}, "property_type": {"name": "property_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "number_of_rooms": {"name": "number_of_rooms", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "monthly_rent": {"name": "monthly_rent", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'ZAR'"}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"properties_owner_id_users_id_fk": {"name": "properties_owner_id_users_id_fk", "tableFrom": "properties", "tableTo": "users", "columnsFrom": ["owner_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "sessions": {"name": "sessions", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"sessions_token_unique": {"name": "sessions_token_unique", "columns": ["token"], "isUnique": true}}, "foreignKeys": {"sessions_user_id_users_id_fk": {"name": "sessions_user_id_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "tenants": {"name": "tenants", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "property_id": {"name": "property_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "emergency_contact": {"name": "emergency_contact", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "emergency_phone": {"name": "emergency_phone", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "move_in_date": {"name": "move_in_date", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "move_out_date": {"name": "move_out_date", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"tenants_user_id_users_id_fk": {"name": "tenants_user_id_users_id_fk", "tableFrom": "tenants", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "tenants_property_id_properties_id_fk": {"name": "tenants_property_id_properties_id_fk", "tableFrom": "tenants", "tableTo": "properties", "columnsFrom": ["property_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email_verified": {"name": "email_verified", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'property_owner'"}, "subscription_plan": {"name": "subscription_plan", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'free'"}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"users_email_unique": {"name": "users_email_unique", "columns": ["email"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}