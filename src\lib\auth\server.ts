import { auth } from "./auth"
import { headers } from "next/headers"
import { redirect } from "next/navigation"

export async function getSession() {
  const session = await auth.api.getSession({
    headers: await headers(),
  })
  
  return session
}

export async function requireAuth() {
  const session = await getSession()
  
  if (!session) {
    redirect("/auth/signin")
  }
  
  return session
}

export async function requireRole(allowedRoles: string[]) {
  const session = await requireAuth()
  const user = session.user as { role?: string }

  if (!allowedRoles.includes(user.role || 'property_owner')) {
    redirect("/unauthorized")
  }

  return session
}

export async function requirePropertyOwner() {
  return requireRole(["property_owner", "admin"])
}

export async function requireTenant() {
  return requireRole(["tenant", "admin"])
}

export async function requireAdmin() {
  return requireRole(["admin"])
}
