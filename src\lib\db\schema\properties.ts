import { sqliteTable, text, integer, real } from 'drizzle-orm/sqlite-core'
import { createId } from '@paralleldrive/cuid2'
import { user } from './users'

// Property types enum
export const propertyTypes = ['house', 'apartment', 'condo', 'townhouse', 'studio', 'room'] as const
export type PropertyType = typeof propertyTypes[number]

// Payment currencies
export const currencies = ['ZAR', 'USD', 'BWP', 'ZWL'] as const
export type Currency = typeof currencies[number]

// Properties table
export const properties = sqliteTable('properties', {
  id: text('id').primaryKey().$defaultFn(() => createId()),
  ownerId: text('owner_id').notNull().references(() => user.id, { onDelete: 'cascade' }),
  address: text('address').notNull(),
  city: text('city').notNull(),
  state: text('state').notNull(),
  zipCode: text('zip_code').notNull(),
  country: text('country').notNull().default('South Africa'),
  propertyType: text('property_type', { enum: propertyTypes }).notNull(),
  numberOfRooms: integer('number_of_rooms').notNull(),
  description: text('description'),
  monthlyRent: real('monthly_rent').notNull(),
  currency: text('currency', { enum: currencies }).notNull().default('ZAR'),
  isActive: integer('is_active', { mode: 'boolean' }).notNull().default(true),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull().$defaultFn(() => new Date()),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull().$defaultFn(() => new Date()),
})

// Export types for TypeScript
export type Property = typeof properties.$inferSelect
export type NewProperty = typeof properties.$inferInsert
