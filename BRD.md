Business Requirements Document (BRD)
1. Introduction
This document outlines the business requirements for a cloud-based real estate management tool. The primary objective is to create a platform that allows property owners to remotely manage their assets, tenants, and lease agreements with a high degree of transparency and efficiency. The application is specifically designed to address the unique challenges of managing properties in non well-served communities, where shared living spaces are common. It aims to solve the challenges faced by landlords who are not constantly on-site, such as manual rent collection, unverified maintenance requests, and disorganized documentation. By centralizing all property-related activities, from tenant onboarding to maintenance tracking, the software will serve as a single, intuitive hub that saves time, reduces administrative overhead, and provides peace of mind for property owners.

2. Project Phases & Scope
The project will be delivered in multiple phases, with each phase delivering a cohesive, testable, and independent set of features.

Phase 1: Foundational Core (MVP)
This phase focuses on building the essential features required for a functional and secure application. The features in this phase are a prerequisite for all subsequent releases.

User Registration & Authentication: A streamlined system for new property owners and tenants to securely register and authenticate. The registration process will collect essential user details, and authentication will leverage BetterAuth to ensure a secure login experience.

Property Management: Comprehensive functionality for property owners to create, view, update, and delete their managed properties. This includes capturing key information like address, property type, number of rooms, and other relevant descriptive metadata. The system will support a one-to-many relationship, allowing multiple tenants to be associated with a single property address.

Tenant Management: The ability to create new tenant profiles and link them to specific properties. This will include storing contact information, move-in dates, and maintaining a chronological history of all tenants who have occupied a property.

Digital Lease Signing: A robust feature for tenants to digitally sign their lease agreements, providing a secure and verifiable legal record. The system will capture a digital signature and a timestamp, storing the signed document securely in Cloudflare R2.

Move-in/Move-out Photo Evidence: A feature allowing both tenants and property owners to upload "before" and "after" photo evidence of a property's condition during the move-in and move-out process. This feature will create a timestamped digital record for condition verification.

Role-Based Access Control (RBAC): A foundational security framework that defines and enforces granular permissions for different user roles within the system. This must be implemented from the very beginning to ensure data privacy and security.

Phase 2: Monetization & Enhancement
This phase builds upon the core functionality of Phase 1 by introducing key features that enhance the user experience and enable the project's monetization strategy. The features in this phase are dependent on Phase 1 being complete.

Payment Uploads (Multi-Currency Support): A module allowing tenants to upload evidence of rent payments. This will include support for various file types (e.g., JPG, PDF) and the ability to log payment details such as amount, date, method, and currency. The system will support payments in South African Rand (ZAR), United States Dollar (USD), Botswana Pula (BWP), and Zimbabwean Dollar (ZWL) as default options.

Maintenance Requests: A dedicated portal for tenants to submit maintenance requests. The form will include fields for a detailed description of the issue, a severity level (e.g., minor, urgent), and the ability to upload photo evidence to provide a clear understanding of the problem.

In-App Notifications: A system for generating and displaying in-app notifications based on critical events from payment uploads and maintenance requests. These notifications will be non-intrusive and provide real-time updates.

Subscription Plans: The implementation of a tiered pricing model to offer different levels of access. This will include a Free Plan with limited features (e.g., one property) and a Paid Plan with premium features.

Future Releases (Out-of-Scope for Initial Build)
These features are considered for future releases after the successful deployment and stabilization of the initial two phases.

Agent Management: Functionality for property owners to appoint an Agent to the system. This will add a new user role with specific access permissions.

User Impersonation (Admin Only): A secure administrative feature that allows designated administrators to replicate or "impersonate" a user's account to diagnose and troubleshoot issues.

WhatsApp and SMS notification reminders: Integration with third-party services to send out-of-app notifications for rent reminders and other critical alerts.

3. Key User Roles
The system will define and manage the following primary user roles with specific access levels and permissions:

Property Owner: The core user with full control over all their properties, leases, and tenant information.

Tenant: A user linked to a single property and an active lease agreement. Their access is highly restricted to their own lease, payments, and maintenance requests.

Agent: (Future Release) A user with limited permissions, specifically appointed by a Property Owner to manage assigned properties.

Admin: (Future Release) A superuser with full access to all system functionalities.

4. Functional Requirements
4.1 Phase 1 Functional Requirements
Property Registration: Owners and Agents (with appropriate permissions) will use a guided form to add new properties.

Property Management Dashboard: A dynamic dashboard will provide Owners with an overview of their portfolio.

Lease & Tenant Creation: A step-by-step workflow will guide users through creating new lease agreements.

Digital Lease Signing: Tenants will receive a secure link to their lease agreement for digital signature.

Move-in/Move-out Photo Documentation: Both tenants and owners will upload timestamped photos to document the property's condition.

4.2 Phase 2 Functional Requirements
Payment Upload: The user interface for payment uploads will allow tenants to select their lease, enter the amount, date, and currency, and upload a digital receipt.

Maintenance Request: The maintenance request form will be accessible from the tenant's dashboard, including fields for a detailed description and photo evidence.

In-App Notifications: The system will generate notifications based on triggers like rent due dates and new maintenance requests.

Subscription Plans: The system will include a basic free plan and a paid plan, with features gated accordingly.

5. Non-Functional Requirements
These requirements apply to all phases of the project.

5.1 Technology Stack
The application will be built using the following technologies:

Frontend: Next.js for building a fast, server-rendered React application. The application will primarily leverage Next.js API Routes for most server-side interactions. Server Actions will be used as a fallback only when API Routes are not suitable. The use of the 'use client' directive will be minimized.

Database: Cloudflare D1 (SQLite) will be used for all relational data.

Object Storage: Cloudflare R2 will handle all file uploads.

Authentication: BetterStack (BetterAuth) will be the primary authentication service.

ORM: Drizzle will serve as the ORM.

5.2 Security & Permissions
Authentication: All user authentication and identity management will be robustly handled by BetterAuth.

Role-Based Access Control (RBAC): The system will be designed with a granular RBAC model from the ground up to ensure permissions are strictly enforced at the database level.

5.3 Performance & Reliability
The application must be highly performant, with a goal of page load times under 2 seconds and API response times under 500ms.

The system must be highly reliable, with a target uptime of 99.9%.