import { sqliteTable, text, integer, real } from 'drizzle-orm/sqlite-core'
import { createId } from '@paralleldrive/cuid2'
import { properties, currencies } from './properties'
import { tenants } from './tenants'

// Leases table
export const leases = sqliteTable('leases', {
  id: text('id').primaryKey().$defaultFn(() => createId()),
  propertyId: text('property_id').notNull().references(() => properties.id, { onDelete: 'cascade' }),
  tenantId: text('tenant_id').notNull().references(() => tenants.id, { onDelete: 'cascade' }),
  startDate: integer('start_date', { mode: 'timestamp' }).notNull(),
  endDate: integer('end_date', { mode: 'timestamp' }).notNull(),
  monthlyRent: real('monthly_rent').notNull(),
  currency: text('currency', { enum: currencies }).notNull().default('ZAR'),
  securityDeposit: real('security_deposit'),
  leaseTerms: text('lease_terms').notNull(),
  isSigned: integer('is_signed', { mode: 'boolean' }).notNull().default(false),
  signedAt: integer('signed_at', { mode: 'timestamp' }),
  signatureData: text('signature_data'), // Base64 encoded signature
  documentUrl: text('document_url'), // URL to signed document in R2
  isActive: integer('is_active', { mode: 'boolean' }).notNull().default(true),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull().$defaultFn(() => new Date()),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull().$defaultFn(() => new Date()),
})

// Export types for TypeScript
export type Lease = typeof leases.$inferSelect
export type NewLease = typeof leases.$inferInsert
