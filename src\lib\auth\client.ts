import { createAuthClient } from "better-auth/react"

export const authClient = createAuthClient({
  baseURL: process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000",
})

export const {
  signIn,
  signUp,
  signOut,
  useSession,
  getSession,
} = authClient

// Types will be inferred from the auth client
export type AuthSession = Awaited<ReturnType<typeof getSession>>
export type AuthUser = NonNullable<AuthSession>['user']
