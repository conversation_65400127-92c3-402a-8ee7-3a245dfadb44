import { sqliteTable, text, integer, real } from 'drizzle-orm/sqlite-core'
import { createId } from '@paralleldrive/cuid2'
import { tenants } from './tenants'
import { leases } from './leases'
import { fileUploads } from './file-uploads'
import { currencies } from './properties'
import { user } from './users'

// Payment records table
export const paymentRecords = sqliteTable('payment_records', {
  id: text('id').primaryKey().$defaultFn(() => createId()),
  tenantId: text('tenant_id').notNull().references(() => tenants.id, { onDelete: 'cascade' }),
  leaseId: text('lease_id').notNull().references(() => leases.id, { onDelete: 'cascade' }),
  amount: real('amount').notNull(),
  currency: text('currency', { enum: currencies }).notNull().default('ZAR'),
  paymentDate: integer('payment_date', { mode: 'timestamp' }).notNull(),
  paymentMethod: text('payment_method').notNull(),
  receiptFileId: text('receipt_file_id').references(() => fileUploads.id),
  notes: text('notes'),
  isVerified: integer('is_verified', { mode: 'boolean' }).notNull().default(false),
  verifiedAt: integer('verified_at', { mode: 'timestamp' }),
  verifiedBy: text('verified_by').references(() => user.id),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull().$defaultFn(() => new Date()),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull().$defaultFn(() => new Date()),
})

// Export types for TypeScript
export type PaymentRecord = typeof paymentRecords.$inferSelect
export type NewPaymentRecord = typeof paymentRecords.$inferInsert
