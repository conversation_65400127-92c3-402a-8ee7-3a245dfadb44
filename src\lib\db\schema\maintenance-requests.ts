import { sqliteTable, text, integer } from 'drizzle-orm/sqlite-core'
import { createId } from '@paralleldrive/cuid2'
import { tenants } from './tenants'
import { properties } from './properties'
import { user } from './users'

// Maintenance request severity levels
export const maintenanceSeverity = ['minor', 'moderate', 'urgent', 'emergency'] as const
export type MaintenanceSeverity = typeof maintenanceSeverity[number]

// Maintenance request status
export const maintenanceStatus = ['pending', 'in_progress', 'completed', 'cancelled'] as const
export type MaintenanceStatus = typeof maintenanceStatus[number]

// Maintenance requests table
export const maintenanceRequests = sqliteTable('maintenance_requests', {
  id: text('id').primaryKey().$defaultFn(() => createId()),
  tenantId: text('tenant_id').notNull().references(() => tenants.id, { onDelete: 'cascade' }),
  propertyId: text('property_id').notNull().references(() => properties.id, { onDelete: 'cascade' }),
  title: text('title').notNull(),
  description: text('description').notNull(),
  severity: text('severity', { enum: maintenanceSeverity }).notNull().default('minor'),
  status: text('status', { enum: maintenanceStatus }).notNull().default('pending'),
  photoFileIds: text('photo_file_ids'), // JSON array of file upload IDs
  assignedTo: text('assigned_to').references(() => user.id),
  completedAt: integer('completed_at', { mode: 'timestamp' }),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull().$defaultFn(() => new Date()),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull().$defaultFn(() => new Date()),
})

// Export types for TypeScript
export type MaintenanceRequest = typeof maintenanceRequests.$inferSelect
export type NewMaintenanceRequest = typeof maintenanceRequests.$inferInsert
