import { getSession } from '@/lib/auth/server'
import { redirect } from 'next/navigation'

export default async function DashboardPage() {
  const session = await getSession()

  if (!session) {
    redirect('/auth/signin')
  }

  const user = session.user as {
    name?: string
    email?: string
    role?: string
    subscriptionPlan?: string
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="bg-white shadow rounded-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Welcome to your Dashboard
          </h1>
          <div className="space-y-4">
            <div>
              <p className="text-sm text-gray-600">Name:</p>
              <p className="font-medium">{user.name || 'N/A'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Email:</p>
              <p className="font-medium">{user.email || 'N/A'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Role:</p>
              <p className="font-medium capitalize">{user.role?.replace('_', ' ') || 'property_owner'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Subscription:</p>
              <p className="font-medium capitalize">{user.subscriptionPlan || 'free'}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
