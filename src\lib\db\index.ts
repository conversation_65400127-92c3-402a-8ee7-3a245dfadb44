import { drizzle } from 'drizzle-orm/d1'
import { getCloudflareContext } from '@opennextjs/cloudflare'
import * as schema from './schema'

async function getDatabase() {
  try {
    const { env } = await getCloudflareContext({async: true})

    if (!env.DB) {
      throw new Error('Database binding not found. Make sure DB is configured in wrangler.jsonc')
    }

    return drizzle(env.DB, { schema, logger: true })
  } catch(error) {
    // During build time or when Cloudflare context is not available,
    // return a mock database instance
    console.warn('Cloudflare context not available, using mock database', error);
    return drizzle({} as D1Database, { schema })
  }
}

export const db = await getDatabase()

export { schema }
export * from './schema'
