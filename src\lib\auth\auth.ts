import { betterAuth } from "better-auth"
import { drizzleAdapter } from "better-auth/adapters/drizzle"
import { account, db, session, user, verification } from "../db"

export const auth = betterAuth({
  database: drizzleAdapter(db, {
    provider: "sqlite",
    schema: {
      user: user,
      session: session,     
      account,
      verification,
    },
  }),
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: false, // Set to true in production
  },
  user: {
    additionalFields: {
      role: {
        type: "string",
        defaultValue: "property_owner",
        required: false,
      },
      subscriptionPlan: {
        type: "string",
        defaultValue: "free",
        required: false,
      },
    },
  },
  plugins: [],
})

// Export the auth instance for use in other files
export { auth as default }
